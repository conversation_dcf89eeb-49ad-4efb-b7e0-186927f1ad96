<script setup>
import { useConfigStore } from '@core/stores/config'
import { AppContentLayoutNav } from '@layouts/enums'
import { switchToVerticalNavOnLtOverlayNavBreakpoint } from '@layouts/utils'
import { useSubscriptionGuard } from '@/composables/useSubscriptionGuard'
import { useSocketStore } from '@stores/auth'
import { socket } from '@socket/socket'
import SubscriptionBlocker from '@/components/SubscriptionBlocker.vue'

const DefaultLayoutWithHorizontalNav = defineAsyncComponent(() => import('./components/DefaultLayoutWithHorizontalNav.vue'))
const DefaultLayoutWithVerticalNav = defineAsyncComponent(() => import('./components/DefaultLayoutWithVerticalNav.vue'))
const configStore = useConfigStore()
const userStore = useSocketStore()

const { user } = storeToRefs(userStore)

const router = useRouter()

// Subscription guard setup
const {
  isBlocked,
  blockReason,
  subscriptionDetails,
  checkSubscriptionStatus,
  handleSubscriptionResponse,
  startPeriodicCheck,
  stopPeriodicCheck,
} = useSubscriptionGuard()

// ℹ️ This will switch to vertical nav when define breakpoint is reached when in horizontal nav layout

// Remove below composable usage if you are not using horizontal nav layout in your app
switchToVerticalNavOnLtOverlayNavBreakpoint()

const { layoutAttrs, injectSkinClasses } = useSkins()

injectSkinClasses()

// SECTION: Loading Indicator
const isFallbackStateActive = ref(false)
const refLoadingIndicator = ref(null)

watch([
  isFallbackStateActive,
  refLoadingIndicator,
], () => {
  if (isFallbackStateActive.value && refLoadingIndicator.value)
    refLoadingIndicator.value.fallbackHandle()
  if (!isFallbackStateActive.value && refLoadingIndicator.value)
    refLoadingIndicator.value.resolveHandle()
}, { immediate: true })
// !SECTION

// Check subscription status when layout mounts
onMounted(() => {
  console.log('Layout mounted, current route:', router.currentRoute.value.path)
  if (userStore.user?._id) {
    console.log('User found, checking subscription status')
    checkSubscriptionStatus()
    startPeriodicCheck()
  } else {
    console.log('No user found, skipping subscription check')
  }
})

// Listen for subscription details response
socket.on('getUserSubscriptionDetails', handleSubscriptionResponse)

// Watch for changes in isBlocked for debugging
watch(isBlocked, newValue => {
  console.log('Layout: isBlocked changed to:', newValue, 'on route:', router.currentRoute.value.path)
})

// Clean up socket listeners and periodic check
onUnmounted(() => {
  socket.off('getUserSubscriptionDetails', handleSubscriptionResponse)
  stopPeriodicCheck()
})
</script>

<template>
  <Component
    v-bind="layoutAttrs"
    :is="configStore.appContentLayoutNav === AppContentLayoutNav.Vertical ? DefaultLayoutWithVerticalNav : DefaultLayoutWithHorizontalNav"
  >
    <AppLoadingIndicator ref="refLoadingIndicator" />

    <RouterView v-slot="{ Component }">
      <Suspense
        :timeout="0"
        @fallback="isFallbackStateActive = true"
        @resolve="isFallbackStateActive = false"
      >
        <Component :is="Component" />
      </Suspense>
    </RouterView>

    <!-- Subscription Blocker -->
    <SubscriptionBlocker
      :is-blocked="isBlocked"
      :block-reason="blockReason"
      :subscription-details="subscriptionDetails"
      @upgrade="checkSubscriptionStatus"
      @logout="userStore.logout({ id: user._id, user: user._id })"
    />

    <!-- Debug info -->
    <div
      v-if="false"
      style="position: fixed; top: 10px; right: 10px; background: white; padding: 10px; border: 1px solid black; z-index: 9999; font-size: 12px;"
    >
      <div>Route: {{ $route.path }}</div>
      <div>Is Blocked: {{ isBlocked }}</div>
      <div>Block Reason: {{ blockReason }}</div>
    </div>
  </Component>
</template>

<style lang="scss">
// As we are using `layouts` plugin we need its styles to be imported
@use "@layouts/styles/default-layout";
</style>
