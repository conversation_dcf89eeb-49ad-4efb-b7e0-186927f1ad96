const mongoose = require('mongoose')

const UserSubscriptionSchema = new mongoose.Schema({
  planCode: {
    type: String,
  },
  paystackSubscriptionCode: {
    type: String,
  },
  paystackCustomerCode: {
    type: String,
  },
  createdAt: {
    type: Date,
    default: Date.now,
  },
  status: {
    type: String,
    enum: ['trial', 'active', 'expired'],
    default: 'trial',
  },
  currentPackage: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'SubscriptionPackage',
  },
  startedAt: {
    type: Date,
    default: Date.now,
  },
  endsAt: {
    type: Date,
  },
  hasUsedTrial: {
    type: Boolean,
    default: false,
  },
  hasPaid: {
    type: Boolean,
    default: false,
  },
  trialEndsAt: {
    type: Date,
  },
  user: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User', required: true,
  },
})

module.exports = mongoose.model('UserSubscription', UserSubscriptionSchema)
