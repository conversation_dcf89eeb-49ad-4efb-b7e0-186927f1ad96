<script setup>
import { useSocketStore } from '@stores/auth'
import { useSubscriptionSocketStore } from '@stores/subscription'
import { useRoute, useRouter } from 'vue-router'
import { socket } from '@socket/socket'
import { onMounted } from 'vue'

const store = useSocketStore()
const subscriptionStore = useSubscriptionSocketStore()

const { user } = storeToRefs(store)

const route = useRoute()
const router = useRouter()

// Log that we're on the callback page
onMounted(() => {
  console.log('=== CALLBACK PAGE MOUNTED ===')
  console.log('Subscription callback page loaded')
  console.log('Current route path:', route.path)
  console.log('Current route fullPath:', route.fullPath)
  console.log('Current route name:', route.name)
  console.log('Query params:', route.query)
  console.log('=== END CALLBACK PAGE INFO ===')

  subscriptionStore.subscriptionCallback({
    user: user.value._id,
    query: route.query,
  })
})

socket.on('subscriptionCallback', data => {
  console.log('Paystack callback data:', data)

  /*try {
    if (data.status === 'success') {
      // Optionally notify user
      router.replace('/subscription/success') // or show success in-place
    } else {
      router.replace('/subscription/failure')
    }
  } catch (err) {
    console.error('Verification failed:', err)
    router.replace('/subscription/error')
  }*/
})
</script>

<template>
  <div class="pa-8">
    <VContainer>
      <VRow justify="center">
        <VCol
          cols="12"
          md="6"
          class="text-center"
        >
          <VIcon
            icon="tabler-check-circle"
            size="80"
            color="success"
            class="mb-4"
          />
          <h2 class="text-h4 mb-4">
            Processing Payment
          </h2>
          <p class="text-body-1 mb-6">
            Please wait while we process your subscription payment...
          </p>
          <VProgressCircular
            indeterminate
            color="primary"
            size="40"
          />
        </VCol>
      </VRow>
    </VContainer>
  </div>
</template>


