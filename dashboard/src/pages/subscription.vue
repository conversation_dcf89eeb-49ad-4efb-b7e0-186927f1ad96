<script setup>
import { ref, computed, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useSocketStore } from '@stores/auth'
import { useSubscriptionSocketStore } from '@stores/subscription'
import AppPricing from '@/components/AppPricing.vue'
import PricingPlanDialog from '@/components/dialogs/PricingPlanDialog.vue'
import { toast } from 'vue3-toastify'
import { useTheme } from 'vuetify'

definePage({
  meta: {
    layout: 'default',
    requiresAuth: true,
  },
})

const route = useRoute()
const router = useRouter()
const userStore = useSocketStore()
const subscriptionStore = useSubscriptionSocketStore()
const vuetifyTheme = useTheme()

const isPricingDialogVisible = ref(false)

// Check if user came here due to expired subscription
const isExpired = computed(() => {
  return route.query.expired === 'true'
})

// Handle plan upgrade
const handlePlanUpgrade = upgradeData => {
  console.log('Plan upgrade requested:', upgradeData)

  // Extract plan and billing cycle from upgrade data
  const plan = upgradeData.plan || upgradeData
  const billingCycle = upgradeData.billingCycle || 'monthly'

  if (!userStore.user?._id) {
    toast('Please log in to upgrade your plan', {
      autoClose: 5000,
      theme: vuetifyTheme.global.name.value,
      type: 'error',
    })

    return
  }

  // Directly initiate payment for trial users
  const planType = `${plan.title.toLowerCase().replace(' ', '')}${billingCycle === 'yearly' ? 'Yearly' : 'Monthly'}`
  const price = billingCycle === 'yearly' ? plan.priceYearly : plan.priceMonthly

  // Get the Paystack plan code
  const paystackPlanCode = billingCycle === 'yearly'
    ? plan.paystackPlanCodes?.yearly
    : plan.paystackPlanCodes?.monthly

  if (!paystackPlanCode) {
    toast('Plan configuration error. Please contact support.', {
      autoClose: 5000,
      theme: vuetifyTheme.global.name.value,
      type: 'error',
    })

    return
  }

  console.log('Initiating payment for plan upgrade:', {
    plan: plan.title,
    billingCycle,
    price,
    paystackPlanCode,
  })

  // Use upgradeFromTrial to initiate payment
  subscriptionStore.upgradeFromTrial({
    userId: userStore.user._id,
    email: userStore.user.email,
    paystackPlan: paystackPlanCode,
    planType: planType,
    price: price,
    planId: plan._id,
  })
}

// Handle successful plan upgrade
const handlePlanUpgraded = data => {
  console.log('Plan upgraded successfully:', data)

  toast('Subscription upgraded successfully! Redirecting to dashboard...', {
    autoClose: 3000,
    theme: vuetifyTheme.global.name.value,
    type: 'success',
  })

  // Redirect to dashboard after successful upgrade
  setTimeout(() => {
    router.push('/')
  }, 2000)
}

// Socket listeners
import { socket } from '@socket/socket'

// Listen for Paystack redirect
socket.on('redirectToPaystack', data => {
  console.log('Redirecting to Paystack from subscription page:', data)

  if (data.status === 'success') {
    toast('Redirecting to payment...', {
      autoClose: 3000,
      theme: vuetifyTheme.global.name.value,
      type: 'info',
    })

    // Redirect to Paystack
    window.location.href = data.url
  }
})

// Listen for subscription errors
socket.on('subscriptionError', data => {
  toast(data.message || 'An error occurred during subscription upgrade', {
    autoClose: 5000,
    theme: vuetifyTheme.global.name.value,
    type: 'error',
  })
})

// Load subscription packages on mount
onMounted(() => {
  subscriptionStore.getFrontendSubscriptionPackages()
})
</script>

<template>
  <div class="subscription-page">
    <!-- Header Section -->
    <VContainer class="py-8">
      <VRow justify="center">
        <VCol
          cols="12"
          md="8"
          lg="6"
          class="text-center"
        >
          <div
            v-if="isExpired"
            class="mb-6"
          >
            <VIcon
              icon="tabler-alert-triangle"
              size="64"
              color="warning"
              class="mb-4"
            />
            <h1 class="text-h3 mb-4">
              Your Trial Has Expired
            </h1>
            <p class="text-h6 text-medium-emphasis">
              Choose a plan to continue using QwoteZ and unlock all features
            </p>
          </div>
          
          <div
            v-else
            class="mb-6"
          >
            <VIcon
              icon="tabler-crown"
              size="64"
              color="primary"
              class="mb-4"
            />
            <h1 class="text-h3 mb-4">
              Choose Your Plan
            </h1>
            <p class="text-h6 text-medium-emphasis">
              Select the perfect plan for your business needs
            </p>
          </div>
        </VCol>
      </VRow>
    </VContainer>

    <!-- Pricing Section -->
    <VContainer>
      <VRow justify="center">
        <VCol cols="12">
          <AppPricing 
            :show-trial-info="isExpired"
            @upgrade-plan="handlePlanUpgrade"
          />
        </VCol>
      </VRow>
    </VContainer>

    <!-- Features Comparison -->
    <VContainer class="py-8">
      <VRow justify="center">
        <VCol
          cols="12"
          md="10"
        >
          <h2 class="text-h4 text-center mb-8">
            What You Get With Each Plan
          </h2>
          
          <VCard>
            <VCardText>
              <VTable>
                <thead>
                  <tr>
                    <th>Feature</th>
                    <th class="text-center">
                      Basic
                    </th>
                    <th class="text-center">
                      All In
                    </th>
                    <th class="text-center">
                      Production
                    </th>
                    <th class="text-center">
                      No Limits
                    </th>
                  </tr>
                </thead>
                <tbody>
                  <tr>
                    <td>Identities</td>
                    <td class="text-center">
                      1
                    </td>
                    <td class="text-center">
                      5
                    </td>
                    <td class="text-center">
                      15
                    </td>
                    <td class="text-center">
                      Unlimited
                    </td>
                  </tr>
                  <tr>
                    <td>Templates</td>
                    <td class="text-center">
                      Basic
                    </td>
                    <td class="text-center">
                      Premium
                    </td>
                    <td class="text-center">
                      All
                    </td>
                    <td class="text-center">
                      Custom
                    </td>
                  </tr>
                  <tr>
                    <td>Support</td>
                    <td class="text-center">
                      Email
                    </td>
                    <td class="text-center">
                      Priority
                    </td>
                    <td class="text-center">
                      Phone
                    </td>
                    <td class="text-center">
                      Dedicated
                    </td>
                  </tr>
                  <tr>
                    <td>Analytics</td>
                    <td class="text-center">
                      Basic
                    </td>
                    <td class="text-center">
                      Advanced
                    </td>
                    <td class="text-center">
                      Full Suite
                    </td>
                    <td class="text-center">
                      Enterprise
                    </td>
                  </tr>
                </tbody>
              </VTable>
            </VCardText>
          </VCard>
        </VCol>
      </VRow>
    </VContainer>

    <!-- Pricing Plan Dialog -->
    <PricingPlanDialog
      v-model:is-dialog-visible="isPricingDialogVisible"
      @plan-upgraded="handlePlanUpgraded"
    />
  </div>
</template>

<style scoped>
.subscription-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  background-attachment: fixed;
}

.subscription-page :deep(.v-container) {
  position: relative;
  z-index: 1;
}

.subscription-page::before {
  content: '';
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.95);
  z-index: 0;
}
</style>
