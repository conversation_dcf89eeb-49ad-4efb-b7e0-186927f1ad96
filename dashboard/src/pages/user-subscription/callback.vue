<script setup>
import { useRoute, useRouter } from 'vue-router'
import { socket } from '@socket/socket'
import { onMounted } from 'vue'

const route = useRoute()
const router = useRouter()

// Log that we're on the callback page
onMounted(() => {
  console.log('Subscription callback page loaded')
  console.log('Current route:', route.path)
  console.log('Query params:', route.query)
})

socket.on('paystackCallback', data => {
  console.log('Paystack callback data:', data)

  /*try {
    if (data.status === 'success') {
      // Optionally notify user
      router.replace('/subscription/success') // or show success in-place
    } else {
      router.replace('/subscription/failure')
    }
  } catch (err) {
    console.error('Verification failed:', err)
    router.replace('/subscription/error')
  }*/
})
</script>

<template>
  <div class="pa-8">
    <VContainer>
      <VRow justify="center">
        <VCol cols="12" md="6" class="text-center">
          <VIcon
            icon="tabler-check-circle"
            size="80"
            color="success"
            class="mb-4"
          />
          <h2 class="text-h4 mb-4">
            Processing Payment
          </h2>
          <p class="text-body-1 mb-6">
            Please wait while we process your subscription payment...
          </p>
          <VProgressCircular
            indeterminate
            color="primary"
            size="40"
          />
        </VCol>
      </VRow>
    </VContainer>
  </div>
</template>


