<script setup>
import { useRoute, useRouter } from 'vue-router'
import { socket } from '@socket/socket'

const route = useRoute()
const router = useRouter()

socket.on('paystackCallback', data => {
  console.log('Paystack callback data:', data)

  /*try {
    if (data.status === 'success') {
      // Optionally notify user
      router.replace('/subscription/success') // or show success in-place
    } else {
      router.replace('/subscription/failure')
    }
  } catch (err) {
    console.error('Verification failed:', err)
    router.replace('/subscription/error')
  }*/
})
</script>

<template>
  <div>
    <h3>Hello</h3>
  </div>
</template>


