<script setup>
import { useRouter } from 'vue-router'

const router = useRouter()

const testRoutes = [
  { name: 'Callback Page', path: '/user-subscription/callback' },
  { name: 'Dashboard', path: '/' },
  { name: 'Subscription Page', path: '/subscription' },
  { name: 'Login Page', path: '/auth/login' }
]

const navigateTo = (path) => {
  console.log('Navigating to:', path)
  router.push(path)
}
</script>

<template>
  <div class="pa-8">
    <VContainer>
      <VRow>
        <VCol cols="12">
          <h1 class="text-h3 mb-6">Subscription Blocker Test</h1>
          
          <VCard class="mb-6">
            <VCardTitle>Test Navigation</VCardTitle>
            <VCardText>
              <p class="mb-4">Click the buttons below to test subscription blocker behavior on different routes:</p>
              
              <div class="d-flex flex-wrap gap-4">
                <VBtn
                  v-for="route in testRoutes"
                  :key="route.path"
                  @click="navigateTo(route.path)"
                  color="primary"
                  variant="outlined"
                >
                  {{ route.name }}
                </VBtn>
              </div>
            </VCardText>
          </VCard>
          
          <VCard>
            <VCardTitle>Expected Behavior</VCardTitle>
            <VCardText>
              <ul>
                <li><strong>Callback Page:</strong> Should NOT show subscription blocker</li>
                <li><strong>Dashboard:</strong> Should show subscription blocker if trial/subscription expired</li>
                <li><strong>Subscription Page:</strong> Should NOT show subscription blocker</li>
                <li><strong>Login Page:</strong> Should NOT show subscription blocker</li>
              </ul>
              
              <p class="mt-4">
                <strong>Check the browser console for debug logs.</strong>
              </p>
            </VCardText>
          </VCard>
        </VCol>
      </VRow>
    </VContainer>
  </div>
</template>
