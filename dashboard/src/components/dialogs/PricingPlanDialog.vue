<script setup>
import { useSubscriptionSocketStore } from '@stores/subscription'
import { useSocketStore } from '@stores/auth'
import { socket } from '@socket/socket'
import { toast } from 'vue3-toastify'
import { useTheme } from 'vuetify'

const props = defineProps({
  isDialogVisible: {
    type: Boolean,
    required: true,
  },
})

const emit = defineEmits(['update:isDialogVisible', 'plan-upgraded'])
const subscriptionStore = useSubscriptionSocketStore()
const authStore = useSocketStore()
const vuetifyTheme = useTheme()

const { user } = storeToRefs(authStore)

const dialogVisibleUpdate = val => {
  emit('update:isDialogVisible', val)
}

const isUpgrading = ref(false)

// Handle plan upgrade
const handlePlanUpgrade = upgradeData => {
  // Handle both old format (plan object) and new format (upgrade data object)
  const plan = upgradeData.plan || upgradeData
  const billingCycle = upgradeData.billingCycle || 'monthly'

  console.log('Plan upgrade requested:', { plan, billingCycle })

  if (!user.value?._id) {
    toast('Please log in to upgrade your plan', {
      autoClose: 5000,
      theme: vuetifyTheme.global.name.value,
      type: 'error',
    })
    
    return
  }

  isUpgrading.value = true

  // Check if user is on trial or has expired subscription
  // For now, always assume trial users need payment since we're handling upgrades
  // The subscription blocker should handle most upgrade cases
  const isOnTrial = true

  console.log('Plan upgrade - assuming trial user needs payment:', {
    plan: plan.title,
    billingCycle,
    isOnTrial,
  })

  if (isOnTrial) {
    // Use upgradeFromTrial for trial users (requires payment)
    const planType = `${plan.title.toLowerCase().replace(' ', '')}${billingCycle === 'yearly' ? 'Yearly' : 'Monthly'}`
    const price = billingCycle === 'yearly' ? plan.priceYearly : plan.priceMonthly

    // Get the Paystack plan code
    const paystackPlanCode = billingCycle === 'yearly'
      ? plan.paystackPlanCodes?.yearly
      : plan.paystackPlanCodes?.monthly

    if (!paystackPlanCode) {
      toast('Plan configuration error. Please contact support.', {
        autoClose: 5000,
        theme: vuetifyTheme.global.name.value,
        type: 'error',
      })
      isUpgrading.value = false
      
      return
    }

    subscriptionStore.upgradeFromTrial({
      userId: user.value._id,
      email: user.value.email,
      paystackPlan: paystackPlanCode,
      planType: planType,
      price: price,
      planId: plan._id,
    })
  } else {
    // Use updateUserSubscription for existing paid users (no payment required)
    subscriptionStore.updateUserSubscription({
      id: user.value._id,
      user: user.value._id,
      subscriptionPlanId: plan._id,
      billingCycle: billingCycle,
    })
  }
}

// Socket listeners for upgrade response
socket.on('updateUserSubscription', data => {
  isUpgrading.value = false

  switch (data.status) {
  case 'success':
    const {
      newSubscription,
      paystackUpdated,
      paystackMethod,
      paystackUpdateLink,
      requiresUserAction,
      billingCycle,
    } = data.data

    let successMessage = `Successfully upgraded to ${newSubscription.title}`
    if (billingCycle) {
      successMessage += ` (${billingCycle} billing)`
    }

    // Handle different Paystack update scenarios
    if (paystackMethod === 'replacement') {
      // Automatic update successful
      toast(successMessage + '. Payment subscription updated automatically.', {
        autoClose: 7000,
        theme: vuetifyTheme.global.name.value,
        type: 'success',
      })

    } else if (paystackUpdated === false) {
      // Update failed but local update succeeded
      toast(successMessage + '. Note: Payment provider update may need manual verification.', {
        autoClose: 8000,
        theme: vuetifyTheme.global.name.value,
        type: 'warning',
      })

    } else {
      // Standard success
      toast(successMessage, {
        autoClose: 7000,
        theme: vuetifyTheme.global.name.value,
        type: 'success',
      })
    }

    emit('plan-upgraded', data.data)
    dialogVisibleUpdate(false)
    break

  case 'error':
    toast(data.message, {
      autoClose: 7000,
      theme: vuetifyTheme.global.name.value,
      type: 'error',
    })
    break
  }
})

// Listen for trial upgrade redirect to Paystack
socket.on('redirectToPaystack', data => {
  console.log('Redirecting to Paystack for trial upgrade:', data)

  if (data.status === 'success' && data.data?.upgradeFromTrial) {
    isUpgrading.value = false

    toast('Redirecting to payment...', {
      autoClose: 3000,
      theme: vuetifyTheme.global.name.value,
      type: 'info',
    })

    // Redirect to Paystack
    window.location.href = data.url
  }
})

// Listen for subscription errors
socket.on('subscriptionError', data => {
  isUpgrading.value = false

  toast(data.message || 'An error occurred during subscription upgrade', {
    autoClose: 5000,
    theme: vuetifyTheme.global.name.value,
    type: 'error',
  })
})

onUnmounted(() => {
  socket.off('updateUserSubscription')
  socket.off('redirectToPaystack')
  socket.off('subscriptionError')
})
</script>

<template>
  <VDialog
    :model-value="props.isDialogVisible"
    :width="$vuetify.display.smAndDown ? 'auto' : 1200"
    @update:model-value="dialogVisibleUpdate"
  >
    <!-- 👉 Dialog close btn -->
    <DialogCloseBtn @click="$emit('update:isDialogVisible', false)" />

    <VCard class="pricing-dialog pa-2 pa-sm-10">
      <VCardText>
        <AppPricing
          md="4"
          @upgrade-plan="handlePlanUpgrade"
        />
      </VCardText>
    </VCard>
  </VDialog>
</template>
